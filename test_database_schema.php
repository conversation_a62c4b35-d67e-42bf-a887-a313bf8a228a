<?php
// Test database schema and constraints
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Database Schema Test</h1>";

require_once 'config/db.php';
$conn = getConnection();

if (!$conn) {
    echo "<p style='color: red;'>✗ Database connection failed</p>";
    exit;
}

echo "<p style='color: green;'>✓ Database connection successful</p>";

// Check enseignant table structure
echo "<h2>Enseignant Table Structure</h2>";
$result = mysqli_query($conn, "DESCRIBE enseignant");
if ($result) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['Field'] . "</td>";
        echo "<td>" . $row['Type'] . "</td>";
        echo "<td>" . $row['Null'] . "</td>";
        echo "<td>" . $row['Key'] . "</td>";
        echo "<td>" . ($row['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . $row['Extra'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>✗ Error describing enseignant table: " . mysqli_error($conn) . "</p>";
}

// Check departement table data
echo "<h2>Available Departments</h2>";
$result = mysqli_query($conn, "SELECT * FROM departement");
if ($result) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Name</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id_departement'] . "</td>";
        echo "<td>" . $row['nom_dep'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>✗ Error fetching departments: " . mysqli_error($conn) . "</p>";
}

// Check specialite table data
echo "<h2>Available Specialites</h2>";
$result = mysqli_query($conn, "SELECT * FROM specialite LIMIT 10");
if ($result) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Name</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['nom'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>✗ Error fetching specialites: " . mysqli_error($conn) . "</p>";
}

// Check filiere table data
echo "<h2>Available Filieres</h2>";
$result = mysqli_query($conn, "SELECT * FROM filiere LIMIT 10");
if ($result) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Dept ID</th></tr>";
    while ($row = mysqli_fetch_assoc($result)) {
        echo "<tr>";
        echo "<td>" . $row['id_filiere'] . "</td>";
        echo "<td>" . $row['nom_filiere'] . "</td>";
        echo "<td>" . $row['id_dep'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: red;'>✗ Error fetching filieres: " . mysqli_error($conn) . "</p>";
}

// Test a sample INSERT to see what fails
echo "<h2>Testing Sample Insert</h2>";
$testData = [
    'CNI' => 'TESTINSERT123',
    'nom' => 'TestNom',
    'prenom' => 'TestPrenom',
    'email' => '<EMAIL>',
    'tele' => '',
    'date_naissance' => '1990-01-01',
    'lieu_naissance' => 'Non spécifié',
    'sexe' => 'masculin',
    'ville' => 'Non spécifié',
    'pays' => 'Maroc',
    'role' => 'vacataire',
    'id_departement' => 1,
    'id_specialite' => 1,
    'date_debut_travail' => date('Y-m-d'),
    'profile_picture' => null
];

$query = "INSERT INTO enseignant (CNI, nom, prenom, email, tele, date_naissance, lieu_naissance, sexe, ville, pays, role, id_departement, id_specialite, date_debut_travail, profile_picture)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

$stmt = mysqli_prepare($conn, $query);
if ($stmt) {
    mysqli_stmt_bind_param($stmt, "sssssssssssiiis",
        $testData['CNI'], $testData['nom'], $testData['prenom'], $testData['email'], $testData['tele'],
        $testData['date_naissance'], $testData['lieu_naissance'], $testData['sexe'], $testData['ville'],
        $testData['pays'], $testData['role'], $testData['id_departement'], $testData['id_specialite'], 
        $testData['date_debut_travail'], $testData['profile_picture']
    );
    
    if (mysqli_stmt_execute($stmt)) {
        echo "<p style='color: green;'>✓ Test insert successful</p>";
        // Clean up test record
        mysqli_query($conn, "DELETE FROM enseignant WHERE CNI = 'TESTINSERT123'");
    } else {
        echo "<p style='color: red;'>✗ Test insert failed: " . mysqli_stmt_error($stmt) . "</p>";
    }
    mysqli_stmt_close($stmt);
} else {
    echo "<p style='color: red;'>✗ Failed to prepare statement: " . mysqli_error($conn) . "</p>";
}

mysqli_close($conn);
?>
