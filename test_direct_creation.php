<?php
// Direct test of vacataire account creation
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Direct Vacataire Account Creation Test</h1>";

// Start session for authentication simulation
session_start();

// Simulate coordinator session
$_SESSION['user'] = [
    'username' => 'TESTCOORD',
    'role' => 'coordinateur',
    'prenom' => 'Test',
    'nom' => 'Coordinator',
    'filiere_name' => 'Test Filiere',
    'filiere_id' => 1
];

echo "<p style='color: blue;'>ℹ Simulated coordinator session</p>";

// Include required files
require_once 'controller/createVacataireAccountController.php';

echo "<p style='color: green;'>✓ Controller loaded successfully</p>";

// Test data
$testData = [
    'CNI' => 'TESTVACAT456',
    'email' => '<EMAIL>',
    'nom' => 'TestNom',
    'prenom' => 'TestPrenom',
    'filiere' => 1,
    'specialite' => 1,
    'role' => 'vacataire'
];

echo "<h2>Test Data</h2>";
echo "<pre>" . json_encode($testData, JSON_PRETTY_PRINT) . "</pre>";

// Simulate POST data
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['CONTENT_TYPE'] = 'application/json';

// Create a temporary file to simulate php://input
$tempFile = tempnam(sys_get_temp_dir(), 'test_input');
file_put_contents($tempFile, json_encode($testData));

// Override php://input for testing
function file_get_contents_override($filename) {
    global $tempFile;
    if ($filename === 'php://input') {
        return file_get_contents($tempFile);
    }
    return file_get_contents($filename);
}

echo "<h2>Testing Account Creation</h2>";

try {
    // Capture output
    ob_start();
    
    // Call the function directly
    createVacataireAccountAPI();
    
    $output = ob_get_clean();
    
    echo "<p style='color: green;'>✓ Function executed without fatal errors</p>";
    echo "<h3>Output:</h3>";
    echo "<pre>" . htmlspecialchars($output) . "</pre>";
    
} catch (Exception $e) {
    ob_end_clean();
    echo "<p style='color: red;'>✗ Exception occurred: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
} catch (Error $e) {
    ob_end_clean();
    echo "<p style='color: red;'>✗ Fatal error occurred: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

// Clean up
unlink($tempFile);

echo "<h2>Manual Testing Instructions</h2>";
echo "<p>If the direct test shows issues, try these steps:</p>";
echo "<ol>";
echo "<li>Go to the create vacataire account page</li>";
echo "<li>Fill out the form with the test data above</li>";
echo "<li>Open browser developer tools (F12)</li>";
echo "<li>Submit the form and check for errors in Console and Network tabs</li>";
echo "</ol>";
?>
