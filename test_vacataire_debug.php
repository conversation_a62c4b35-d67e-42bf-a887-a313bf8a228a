<?php
// Test script to debug vacataire account creation
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Vacataire Account Creation Debug Test</h1>";

// Test database connection
require_once 'config/db.php';
$conn = getConnection();
if ($conn) {
    echo "<p style='color: green;'>✓ Database connection successful</p>";
} else {
    echo "<p style='color: red;'>✗ Database connection failed</p>";
    exit;
}

// Test departement data
echo "<h2>Testing Departements</h2>";
require_once 'model/departementModel.php';
$departements = getAllDepartements();
if (isset($departements['error'])) {
    echo "<p style='color: red;'>✗ Error fetching departements: " . $departements['error'] . "</p>";
} else {
    echo "<p style='color: green;'>✓ Found " . count($departements) . " departements:</p>";
    echo "<ul>";
    foreach ($departements as $dept) {
        echo "<li>ID: " . $dept['id_departement'] . " - " . $dept['nom_dep'] . "</li>";
    }
    echo "</ul>";
}

// Test specialite data
echo "<h2>Testing Specialites</h2>";
require_once 'model/specialiteModel.php';
$specialites = getAllSpecialites();
if (isset($specialites['error'])) {
    echo "<p style='color: red;'>✗ Error fetching specialites: " . $specialites['error'] . "</p>";
} else {
    echo "<p style='color: green;'>✓ Found " . count($specialites) . " specialites:</p>";
    echo "<ul>";
    foreach ($specialites as $spec) {
        echo "<li>ID: " . $spec['id'] . " - " . $spec['nom'] . "</li>";
    }
    echo "</ul>";
}

// Test enseignant check
echo "<h2>Testing Enseignant Check</h2>";
require_once 'model/enseignantModel.php';
$testCNI = "TESTCNI123";
$existingEnseignant = getEnseignantByCNI($testCNI);
if ($existingEnseignant) {
    echo "<p style='color: orange;'>⚠ Enseignant with CNI $testCNI already exists</p>";
} else {
    echo "<p style='color: green;'>✓ No enseignant found with CNI $testCNI (good for testing)</p>";
}

// Test user model functions
echo "<h2>Testing User Model</h2>";
require_once 'model/userModel.php';
echo "<p style='color: green;'>✓ User model loaded successfully</p>";

// Test createEnseignant with sample data
echo "<h2>Testing createEnseignant Function</h2>";
$testData = [
    'CNI' => 'TESTCNI123',
    'nom' => 'Test',
    'prenom' => 'User',
    'email' => '<EMAIL>',
    'tele' => '',
    'date_naissance' => '1990-01-01',
    'lieu_naissance' => 'Non spécifié',
    'sexe' => 'masculin',
    'ville' => 'Non spécifié',
    'pays' => 'Maroc',
    'role' => 'vacataire',
    'id_departement' => 1, // Assuming first department exists
    'id_specialite' => 1,  // Assuming first specialite exists
    'date_debut_travail' => date('Y-m-d')
];

echo "<p>Test data: " . json_encode($testData) . "</p>";

// Don't actually create the enseignant, just test the validation
echo "<p style='color: blue;'>ℹ Test data prepared (not creating actual record)</p>";

// Test form submission simulation
echo "<h2>Testing Form Submission Simulation</h2>";

// Simulate a POST request to the controller
$testFormData = [
    'CNI' => 'TESTVACAT123',
    'email' => '<EMAIL>',
    'nom' => 'TestNom',
    'prenom' => 'TestPrenom',
    'filiere' => 1, // Assuming first filiere exists
    'specialite' => 1, // Assuming first specialite exists
    'role' => 'vacataire'
];

echo "<p>Test form data: " . json_encode($testFormData) . "</p>";

// Check if the route file exists
$routeFile = __DIR__ . '/route/createVacataireAccountRoute.php';
if (file_exists($routeFile)) {
    echo "<p style='color: green;'>✓ Route file exists: $routeFile</p>";
} else {
    echo "<p style='color: red;'>✗ Route file missing: $routeFile</p>";
}

// Check if the controller file exists
$controllerFile = __DIR__ . '/controller/createVacataireAccountController.php';
if (file_exists($controllerFile)) {
    echo "<p style='color: green;'>✓ Controller file exists: $controllerFile</p>";
} else {
    echo "<p style='color: red;'>✗ Controller file missing: $controllerFile</p>";
}

echo "<h2>Summary</h2>";
echo "<p>If all tests above show green checkmarks, the issue might be in the form submission or JavaScript.</p>";
echo "<p>Check the browser's developer console for JavaScript errors when submitting the form.</p>";
echo "<p><strong>Next steps:</strong></p>";
echo "<ul>";
echo "<li>1. Fill out the form with test data (CNI: TESTVACAT123, Email: <EMAIL>, etc.)</li>";
echo "<li>2. Open browser developer tools (F12) and check the Console tab</li>";
echo "<li>3. Submit the form and look for any JavaScript errors or network errors</li>";
echo "<li>4. Check the Network tab to see if the POST request is being made correctly</li>";
echo "</ul>";
?>
