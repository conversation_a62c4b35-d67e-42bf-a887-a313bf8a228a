<?php
require_once "../config/db.php";

function getAllDepartements() {
    $conn = getConnection();
    $query = "SELECT id_departement, nom_dep FROM departement";
    $result = mysqli_query($conn, $query);
    return mysqli_fetch_all($result, MYSQLI_ASSOC);
}

function getDepartementById($id) {
    $conn = getConnection();

    if (!$conn) {
        return ["error" => "Database connection error"];
    }

    $id = mysqli_real_escape_string($conn, $id);
    $sql = "SELECT * FROM departement WHERE id_departement = '$id'";
    $result = mysqli_query($conn, $sql);

    if ($result && mysqli_num_rows($result) > 0) {
        $departement = mysqli_fetch_assoc($result);
        mysqli_free_result($result);
        mysqli_close($conn);
        return $departement;
    } else {
        mysqli_close($conn);
        return ["error" => "No departement found with this ID"];
    }
}

/**
 * Get all filières belonging to a specific department
 *
 * @param int $departmentId The department ID
 * @return array Array of filières
 */
function getFilieresByDepartement($departmentId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getFilieresByDepartement");
        return ["error" => "Database connection error"];
    }

    $departmentId = mysqli_real_escape_string($conn, $departmentId);

    $sql = "SELECT * FROM filiere WHERE id_dep = '$departmentId' ORDER BY nom_filiere";
    error_log("Executing query in getFilieresByDepartement: " . $sql);

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getFilieresByDepartement: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching filières: " . $error];
    }

    $filieres = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $filieres[] = $row;
    }

    mysqli_close($conn);
    return $filieres;
}

/**
 * Get all modules belonging to filières within a specific department
 *
 * @param int $departmentId The department ID
 * @return array Array of modules grouped by filière
 */
function getModulesByDepartement($departmentId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getModulesByDepartement");
        return ["error" => "Database connection error"];
    }

    $departmentId = mysqli_real_escape_string($conn, $departmentId);

    // First get all filières in this department
    $filieres = getFilieresByDepartement($departmentId);

    if (isset($filieres['error'])) {
        mysqli_close($conn);
        return $filieres; // Return the error
    }

    $modulesByFiliere = [];

    // For each filière, get its modules
    foreach ($filieres as $filiere) {
        $filiereId = $filiere['id_filiere'];

        $sql = "SELECT m.*, f.nom_filiere, n.nom as niveau, s.nom as nom_specialite, sem.nom as semestre
                FROM module m
                LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
                LEFT JOIN niveaux n ON m.id_niveau = n.id
                LEFT JOIN specialite s ON m.specialite_id = s.id
                LEFT JOIN semestre sem ON m.id_semestre = sem.id
                WHERE m.filiere_id = '$filiereId'
                ORDER BY m.nom";

        $result = mysqli_query($conn, $sql);

        if (!$result) {
            $error = mysqli_error($conn);
            error_log("Error in getModulesByDepartement for filière $filiereId: " . $error);
            continue; // Skip this filière and continue with others
        }

        $modules = [];
        while ($row = mysqli_fetch_assoc($result)) {
            $modules[] = $row;
        }

        // Only add filière to the result if it has modules
        if (!empty($modules)) {
            $modulesByFiliere[] = [
                'filiere' => $filiere,
                'modules' => $modules
            ];
        }
    }

    mysqli_close($conn);
    return $modulesByFiliere;
}

/**
 * Get all teaching units for a specific module
 *
 * @param int $moduleId The module ID
 * @return array Array of teaching units
 */
function getUniteEnseignementByModule($moduleId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getUniteEnseignementByModule");
        return ["error" => "Database connection error"];
    }

    $moduleId = mysqli_real_escape_string($conn, $moduleId);

    $sql = "SELECT ue.*,
            m.nom as nom_module, m.volume_total, m.id_semestre, s.nom as semestre, m.is_cours, m.is_td, m.is_tp,
            f.nom_filiere,
            n.nom as niveau
            FROM uniteenseignement ue
            LEFT JOIN module m ON ue.module_id = m.id
            LEFT JOIN filiere f ON m.filiere_id = f.id_filiere
            LEFT JOIN niveaux n ON m.id_niveau = n.id
            LEFT JOIN semestre s ON m.id_semestre = s.id
            WHERE ue.module_id = '$moduleId'
            ORDER BY ue.type";

    $result = mysqli_query($conn, $sql);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getUniteEnseignementByModule: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching teaching units: " . $error];
    }

    $unites = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $unites[] = $row;
    }

    mysqli_close($conn);
    return $unites;
}
?>