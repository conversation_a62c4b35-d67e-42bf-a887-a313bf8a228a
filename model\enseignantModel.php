<?php
require_once "../config/db.php";

function getAllEnseignants() {
    $conn = getConnection();
    $query = "SELECT e.*, d.nom_dep as departement, s.nom as specialite
              FROM enseignant e
              LEFT JOIN departement d ON e.id_departement = d.id_departement
              LEFT JOIN specialite s ON e.id_specialite = s.id";
    $result = mysqli_query($conn, $query);
    return mysqli_fetch_all($result, MYSQLI_ASSOC);
}

/**
 * Récupère tous les enseignants d'un département spécifique
 *
 * @param int $departementId L'ID du département
 * @return array Liste des enseignants du département
 */
function getEnseignantsByDepartement($departementId) {
    $conn = getConnection();

    // Sanitize the input
    $departementId = mysqli_real_escape_string($conn, $departementId);

    $query = "SELECT e.*, d.nom_dep as departement, s.nom as specialite
              FROM enseignant e
              LEFT JOIN departement d ON e.id_departement = d.id_departement
              LEFT JOIN specialite s ON e.id_specialite = s.id
              WHERE e.id_departement = '$departementId'
              ORDER BY e.nom, e.prenom";

    $result = mysqli_query($conn, $query);

    if (!$result) {
        return ['error' => 'Error retrieving teachers: ' . mysqli_error($conn)];
    }

    return mysqli_fetch_all($result, MYSQLI_ASSOC);
}

function getEnseignantByCNI($cni) {
    $conn = getConnection();
    $query = "SELECT e.*, d.nom_dep as departement, s.nom as specialite
              FROM enseignant e
              LEFT JOIN departement d ON e.id_departement = d.id_departement
              LEFT JOIN specialite s ON e.id_specialite = s.id
              WHERE e.CNI = ?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "s", $cni);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);
    return mysqli_fetch_assoc($result);
}

function createEnseignant($data) {
    try {
        $conn = getConnection();

        if (!$conn) {
            error_log("Erreur de connexion à la base de données dans createEnseignant");
            return false;
        }

        // Vérifier si l'enseignant existe déjà
        $cni = mysqli_real_escape_string($conn, $data['CNI']);
        $checkQuery = "SELECT COUNT(*) as count FROM enseignant WHERE CNI = '$cni'";
        $checkResult = mysqli_query($conn, $checkQuery);

        if ($checkResult) {
            $row = mysqli_fetch_assoc($checkResult);
            if ($row['count'] > 0) {
                error_log("Un enseignant avec le CNI $cni existe déjà");
                return false;
            }
        }

        // Préparer les données
        $id_departement = isset($data['department']) ? $data['department'] : (isset($data['id_departement']) ? $data['id_departement'] : null);
        $id_specialite = isset($data['specialite']) ? $data['specialite'] : (isset($data['id_specialite']) ? $data['id_specialite'] : null);

        // Traiter correctement les dates
        $date_naissance = null;
        if (isset($data['date_naissance']) && $data['date_naissance'] !== '') {
            // Vérifier si la date est au format valide
            $date_obj = date_create($data['date_naissance']);
            if ($date_obj) {
                $date_naissance = date_format($date_obj, 'Y-m-d');
            }
        }

        // Traiter correctement la date de début
        $date_debut_travail = null;
        if (isset($data['date_debut_travail']) && $data['date_debut_travail'] !== '') {
            // Vérifier si la date est au format valide
            $date_obj = date_create($data['date_debut_travail']);
            if ($date_obj) {
                $date_debut_travail = date_format($date_obj, 'Y-m-d');
            }
        }

        // Si la date de début est toujours null, utiliser la date actuelle
        if ($date_debut_travail === null) {
            $date_debut_travail = date('Y-m-d');
        }

        // Traiter l'image de profil
        $profile_picture = isset($data['profile_picture']) ? $data['profile_picture'] : null;

        // Log des données pour le débogage
        error_log("Données reçues pour createEnseignant: " . json_encode($data));
        error_log("id_departement: " . $id_departement);
        error_log("id_specialite: " . $id_specialite);
        error_log("date_naissance: " . $date_naissance);
        error_log("date_debut_travail: " . $date_debut_travail);
        error_log("profile_picture: " . $profile_picture);

        $query = "INSERT INTO enseignant (CNI, nom, prenom, email, tele, date_naissance, lieu_naissance, sexe, ville, pays, role, id_departement, id_specialite, date_debut_travail, profile_picture)
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = mysqli_prepare($conn, $query);

        if (!$stmt) {
            error_log("Erreur de préparation de la requête: " . mysqli_error($conn));
            return false;
        }

        mysqli_stmt_bind_param($stmt, "sssssssssssiiis",
            $data['CNI'], $data['nom'], $data['prenom'], $data['email'], $data['tele'],
            $date_naissance, $data['lieu_naissance'], $data['sexe'], $data['ville'],
            $data['pays'], $data['role'], $id_departement, $id_specialite, $date_debut_travail, $profile_picture
        );

        $result = mysqli_stmt_execute($stmt);

        if (!$result) {
            $error = mysqli_stmt_error($stmt);
            error_log("Erreur lors de l'exécution de la requête createEnseignant: " . $error);
            error_log("Query was: " . $query);
            error_log("Parameters were: " . json_encode([
                'CNI' => $data['CNI'],
                'nom' => $data['nom'],
                'prenom' => $data['prenom'],
                'email' => $data['email'],
                'tele' => $data['tele'],
                'date_naissance' => $date_naissance,
                'lieu_naissance' => $data['lieu_naissance'],
                'sexe' => $data['sexe'],
                'ville' => $data['ville'],
                'pays' => $data['pays'],
                'role' => $data['role'],
                'id_departement' => $id_departement,
                'id_specialite' => $id_specialite,
                'date_debut_travail' => $date_debut_travail,
                'profile_picture' => $profile_picture
            ]));
        } else {
            error_log("Enseignant created successfully with CNI: " . $data['CNI']);
        }

        mysqli_stmt_close($stmt);
        return $result;
    } catch (Exception $e) {
        error_log("Exception dans createEnseignant: " . $e->getMessage());
        return false;
    }
}

function updateEnseignant($cni, $data) {
    try {
        $conn = getConnection();

        // Log des données reçues pour le débogage
        error_log("Données reçues pour updateEnseignant: " . json_encode($data));

        // Vérifier et préparer les données
        $nom = isset($data['nom']) ? $data['nom'] : '';
        $prenom = isset($data['prenom']) ? $data['prenom'] : '';
        $email = isset($data['email']) ? $data['email'] : '';
        $tele = isset($data['tele']) && $data['tele'] !== '' ? $data['tele'] : null;

        // Traiter correctement les dates
        $date_naissance = null;
        if (isset($data['date_naissance']) && $data['date_naissance'] !== '') {
            // Vérifier si la date est au format valide
            $date_obj = date_create($data['date_naissance']);
            if ($date_obj) {
                $date_naissance = date_format($date_obj, 'Y-m-d');
            }
        }

        $lieu_naissance = isset($data['lieu_naissance']) && $data['lieu_naissance'] !== '' ? $data['lieu_naissance'] : null;
        $sexe = isset($data['sexe']) ? $data['sexe'] : '';
        $ville = isset($data['ville']) && $data['ville'] !== '' ? $data['ville'] : null;
        $pays = isset($data['pays']) && $data['pays'] !== '' ? $data['pays'] : null;
        $role = isset($data['role']) ? $data['role'] : 'enseignant';

        // Gérer les clés étrangères
        $id_departement = isset($data['department']) ? $data['department'] : (isset($data['id_departement']) && $data['id_departement'] !== '' ? $data['id_departement'] : null);
        $id_specialite = isset($data['specialite']) ? $data['specialite'] : (isset($data['id_specialite']) && $data['id_specialite'] !== '' ? $data['id_specialite'] : null);

        // Traiter correctement la date de début
        $date_debut_travail = null;
        if (isset($data['date_debut_travail']) && $data['date_debut_travail'] !== '') {
            // Vérifier si la date est au format valide
            $date_obj = date_create($data['date_debut_travail']);
            if ($date_obj) {
                $date_debut_travail = date_format($date_obj, 'Y-m-d');
            }
        }

        // Si la date de début est toujours null, utiliser la date actuelle
        if ($date_debut_travail === null) {
            $date_debut_travail = date('Y-m-d');
        }

        // Traiter l'image de profil
        $profile_picture = isset($data['profile_picture']) ? $data['profile_picture'] : null;

        // Log des données préparées pour le débogage
        error_log("Données préparées pour updateEnseignant: nom=$nom, prenom=$prenom, email=$email, tele=$tele, date_naissance=$date_naissance, lieu_naissance=$lieu_naissance, sexe=$sexe, ville=$ville, pays=$pays, role=$role, id_departement=$id_departement, id_specialite=$id_specialite, date_debut_travail=$date_debut_travail, profile_picture=$profile_picture, cni=$cni");

        $query = "UPDATE enseignant SET nom=?, prenom=?, email=?, tele=?, date_naissance=?,
                  lieu_naissance=?, sexe=?, ville=?, pays=?, role=?, id_departement=?, id_specialite=?,
                  date_debut_travail=?, profile_picture=?
                  WHERE CNI=?";
        $stmt = mysqli_prepare($conn, $query);

        if (!$stmt) {
            error_log("Erreur de préparation de la requête: " . mysqli_error($conn));
            return false;
        }

        mysqli_stmt_bind_param($stmt, "ssssssssssiisss",
            $nom, $prenom, $email, $tele, $date_naissance,
            $lieu_naissance, $sexe, $ville, $pays,
            $role, $id_departement, $id_specialite, $date_debut_travail, $profile_picture, $cni
        );

        $result = mysqli_stmt_execute($stmt);

        if (!$result) {
            error_log("Erreur lors de l'exécution de la requête: " . mysqli_stmt_error($stmt));
        }

        mysqli_stmt_close($stmt);
        return $result;
    } catch (Exception $e) {
        error_log("Exception dans updateEnseignant: " . $e->getMessage());
        return false;
    }
}

function deleteEnseignant($cni) {
    $conn = getConnection();
    $query = "DELETE FROM enseignant WHERE CNI=?";
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "s", $cni);
    return mysqli_stmt_execute($stmt);
}

/**
 * Search for teachers by name or part of name
 *
 * @param string $searchTerm The search term
 * @return array Array of matching teachers
 */
function searchEnseignants($searchTerm) {
    $conn = getConnection();

    // Sanitize the search term
    $searchTerm = mysqli_real_escape_string($conn, $searchTerm);

    // Search in both nom and prenom fields
    $query = "SELECT e.*, d.nom_dep as departement, s.nom as specialite FROM enseignant e
              LEFT JOIN departement d ON e.id_departement = d.id_departement
              LEFT JOIN specialite s ON e.id_specialite = s.id
              WHERE e.nom LIKE '%$searchTerm%'
              OR e.prenom LIKE '%$searchTerm%'
              ORDER BY e.nom, e.prenom";

    $result = mysqli_query($conn, $query);

    if (!$result) {
        return ['error' => 'Error searching teachers: ' . mysqli_error($conn)];
    }

    return mysqli_fetch_all($result, MYSQLI_ASSOC);
}

/**
 * Format role name for display
 *
 * @param string $role Role from database
 * @return string Formatted role name
 */
function formatRoleName($role) {
    switch ($role) {
        case 'normal':
            return 'Enseignant';
        case 'chef de departement':
            return 'Chef de Département';
        case 'coordinateur':
            return 'Coordinateur';
        case 'vacataire':
            return 'Vacataire';
        case 'chef de filiere':
            return 'Chef de Filière';
        default:
            return ucfirst($role);
    }
}
/**
 * Get fields (filieres) associated with a teacher
 *
 * @param int $teacherId The teacher ID
 * @return array Array of fields associated with the teacher
 */
function getTeacherFields($teacherId) {
    $conn = getConnection();

    if (!$conn) {
        error_log("Database connection error in getTeacherFields");
        return ["error" => "Database connection error"];
    }

    // Sanitize the input
    $teacherId = mysqli_real_escape_string($conn, $teacherId);

    // First, check if the enseignant_filiere table exists
    $checkTable = mysqli_query($conn, "SHOW TABLES LIKE 'enseignant_filiere'");

    if (mysqli_num_rows($checkTable) == 0) {
        // If the table doesn't exist, create it
        error_log("enseignant_filiere table doesn't exist. Creating it.");

        $createTableQuery = "CREATE TABLE IF NOT EXISTS enseignant_filiere (
            id INT AUTO_INCREMENT PRIMARY KEY,
            id_enseignant INT NOT NULL,
            id_filiere INT NOT NULL,
            INDEX (id_enseignant),
            INDEX (id_filiere),
            UNIQUE KEY unique_enseignant_filiere (id_enseignant, id_filiere)
        )";

        $createResult = mysqli_query($conn, $createTableQuery);

        if (!$createResult) {
            error_log("Error creating enseignant_filiere table: " . mysqli_error($conn));
        } else {
            error_log("enseignant_filiere table created successfully.");

            // After creating the table, add associations for this teacher with all fields
            $allFieldsQuery = "SELECT id_filiere FROM filiere";
            $allFieldsResult = mysqli_query($conn, $allFieldsQuery);

            if ($allFieldsResult) {
                while ($row = mysqli_fetch_assoc($allFieldsResult)) {
                    $filiereId = $row['id_filiere'];
                    $insertQuery = "INSERT IGNORE INTO enseignant_filiere (id_enseignant, id_filiere) VALUES ('$teacherId', '$filiereId')";
                    mysqli_query($conn, $insertQuery);
                    error_log("Added association for teacher ID: $teacherId and field ID: $filiereId");
                }
            }
        }
    }

    // Check again if the table exists (it might have been created above)
    $checkTable = mysqli_query($conn, "SHOW TABLES LIKE 'enseignant_filiere'");

    if (mysqli_num_rows($checkTable) > 0) {
        // If the table exists, use it to get the fields
        $query = "SELECT f.*
                  FROM filiere f
                  JOIN enseignant_filiere ef ON f.id_filiere = ef.id_filiere
                  WHERE ef.id_enseignant = '$teacherId'
                  ORDER BY f.nom_filiere";

        error_log("Using enseignant_filiere table to get fields for teacher ID: $teacherId");
    } else {
        // If the table doesn't exist, get fields based on modules assigned to the teacher
        // First, check if the affectation table exists
        $checkAffectationTable = mysqli_query($conn, "SHOW TABLES LIKE 'affectation'");

        if (mysqli_num_rows($checkAffectationTable) > 0) {
            // If the affectation table exists, use it to get the fields
            $query = "SELECT DISTINCT f.*
                      FROM filiere f
                      JOIN module m ON f.id_filiere = m.filiere_id
                      JOIN uniteenseignement ue ON m.id = ue.module_id
                      JOIN affectation a ON ue.id = a.unite_enseignement_id
                      WHERE a.id_enseignant = '$teacherId'
                      ORDER BY f.nom_filiere";
        } else {
            // If neither table exists, get fields based on seances
            $query = "SELECT DISTINCT f.*
                      FROM filiere f
                      JOIN module m ON f.id_filiere = m.filiere_id
                      JOIN seance s ON m.id_module = s.id_module
                      WHERE s.id_enseignant = '$teacherId'
                      ORDER BY f.nom_filiere";
        }
    }

    $result = mysqli_query($conn, $query);

    if (!$result) {
        $error = mysqli_error($conn);
        error_log("Error in getTeacherFields: " . $error);
        mysqli_close($conn);
        return ["error" => "Error fetching fields: " . $error];
    }

    $fields = [];
    while ($row = mysqli_fetch_assoc($result)) {
        $fields[] = $row;
    }

    mysqli_close($conn);
    return $fields;
}
?>
